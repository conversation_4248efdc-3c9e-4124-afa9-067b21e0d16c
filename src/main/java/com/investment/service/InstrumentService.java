package com.investment.service;

import com.investment.api.model.CreateInstrumentRequest;
import com.investment.database.DatabaseManager;
import com.investment.model.Instrument;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.time.LocalDateTime;

/**
 * Service layer for financial instrument operations.
 * Handles business logic for instrument creation, validation, and management.
 */
@Service
public class InstrumentService {
    
    private static final Logger logger = LoggerFactory.getLogger(InstrumentService.class);
    
    @Autowired
    private DatabaseManager databaseManager;
    
    /**
     * Create a new financial instrument.
     * 
     * @param request The instrument creation request
     * @return The created instrument
     * @throws IllegalArgumentException if validation fails
     * @throws SQLException if database operation fails
     */
    public Instrument createInstrument(CreateInstrumentRequest request) throws SQLException {
        logger.info("Creating new instrument: {}", request);
        
        // Validate and normalize the request
        request.validate();
        
        // Check if symbol already exists
        if (databaseManager.symbolExists(request.getSymbol())) {
            throw new IllegalArgumentException("Symbol already exists: " + request.getSymbol());
        }
        
        // Validate symbol format (additional business rules)
        validateSymbolFormat(request.getSymbol());
        
        // Validate market cap if provided
        if (request.getMarketCap() != null && request.getMarketCap().signum() < 0) {
            throw new IllegalArgumentException("Market cap cannot be negative");
        }
        
        // Validate IPO year if provided
        if (request.getIpoYear() != null) {
            int currentYear = LocalDateTime.now().getYear();
            if (request.getIpoYear() > currentYear) {
                throw new IllegalArgumentException("IPO year cannot be in the future");
            }
        }
        
        try {
            // Save to database
            databaseManager.saveInstrumentWithDetails(
                    request.getSymbol(),
                    request.getName(),
                    request.getType().name(),
                    request.getMarketCap(),
                    request.getCountry(),
                    request.getIpoYear(),
                    request.getSector(),
                    request.getIndustry()
            );
            
            // Create and return the instrument object
            Instrument instrument = new Instrument(
                    request.getSymbol(),
                    request.getName(),
                    request.getType(),
                    request.getMarketCap(),
                    request.getCountry(),
                    request.getIpoYear(),
                    request.getSector(),
                    request.getIndustry()
            );
            
            logger.info("Successfully created instrument: {}", request.getSymbol());
            return instrument;
            
        } catch (Exception e) {
            logger.error("Failed to create instrument: {}", request.getSymbol(), e);
            throw new SQLException("Failed to create instrument: " + e.getMessage(), e);
        }
    }
    
    /**
     * Validate symbol format according to business rules.
     * 
     * @param symbol The symbol to validate
     * @throws IllegalArgumentException if symbol format is invalid
     */
    private void validateSymbolFormat(String symbol) {
        if (symbol == null || symbol.trim().isEmpty()) {
            throw new IllegalArgumentException("Symbol cannot be null or empty");
        }
        
        String trimmedSymbol = symbol.trim().toUpperCase();
        
        // Check length
        if (trimmedSymbol.length() > 20) {
            throw new IllegalArgumentException("Symbol cannot exceed 20 characters");
        }
        
        // Check for valid characters (letters, numbers, dots, hyphens)
        if (!trimmedSymbol.matches("^[A-Z0-9.-]+$")) {
            throw new IllegalArgumentException("Symbol must contain only uppercase letters, numbers, dots, and hyphens");
        }
        
        // Additional business rules
        if (trimmedSymbol.startsWith(".") || trimmedSymbol.endsWith(".")) {
            throw new IllegalArgumentException("Symbol cannot start or end with a dot");
        }
        
        if (trimmedSymbol.startsWith("-") || trimmedSymbol.endsWith("-")) {
            throw new IllegalArgumentException("Symbol cannot start or end with a hyphen");
        }
        
        // Check for consecutive special characters
        if (trimmedSymbol.contains("..") || trimmedSymbol.contains("--") || 
            trimmedSymbol.contains(".-") || trimmedSymbol.contains("-.")) {
            throw new IllegalArgumentException("Symbol cannot contain consecutive special characters");
        }
    }
    
    /**
     * Check if a symbol exists in the database.
     * 
     * @param symbol The symbol to check
     * @return true if the symbol exists, false otherwise
     */
    public boolean symbolExists(String symbol) {
        if (symbol == null || symbol.trim().isEmpty()) {
            return false;
        }
        return databaseManager.symbolExists(symbol.trim().toUpperCase());
    }
    
    /**
     * Get an instrument by symbol.
     *
     * @param symbol The symbol to look up
     * @return The instrument if found, null otherwise
     */
    public Instrument getInstrumentBySymbol(String symbol) {
        if (symbol == null || symbol.trim().isEmpty()) {
            return null;
        }

        String upperSymbol = symbol.trim().toUpperCase();

        // Get all instruments and filter by symbol
        // This is not the most efficient approach but works for now
        // In the future, we could add a dedicated method to DatabaseManager
        return databaseManager.getAllInstruments().stream()
                .filter(instrument -> instrument.getSymbol().equals(upperSymbol))
                .findFirst()
                .orElse(null);
    }

    /**
     * Update an existing financial instrument.
     *
     * @param currentSymbol The current symbol of the instrument to update
     * @param request The instrument update request
     * @return The updated instrument
     * @throws IllegalArgumentException if validation fails or instrument not found
     * @throws SQLException if database operation fails
     */
    public Instrument updateInstrument(String currentSymbol, CreateInstrumentRequest request) throws SQLException {
        logger.info("Updating instrument: {} with data: {}", currentSymbol, request);

        if (currentSymbol == null || currentSymbol.trim().isEmpty()) {
            throw new IllegalArgumentException("Current symbol cannot be null or empty");
        }

        String upperCurrentSymbol = currentSymbol.trim().toUpperCase();

        // Check if the instrument exists
        if (!databaseManager.symbolExists(upperCurrentSymbol)) {
            throw new IllegalArgumentException("Instrument not found: " + currentSymbol);
        }

        // Validate and normalize the request
        request.validate();

        // If symbol is being changed, check if new symbol already exists
        String newSymbol = request.getSymbol().toUpperCase();
        if (!upperCurrentSymbol.equals(newSymbol) && databaseManager.symbolExists(newSymbol)) {
            throw new IllegalArgumentException("New symbol already exists: " + request.getSymbol());
        }

        // Validate symbol format (additional business rules)
        validateSymbolFormat(request.getSymbol());

        // Validate market cap if provided
        if (request.getMarketCap() != null && request.getMarketCap().signum() < 0) {
            throw new IllegalArgumentException("Market cap cannot be negative");
        }

        // Validate IPO year if provided
        if (request.getIpoYear() != null) {
            int currentYear = LocalDateTime.now().getYear();
            if (request.getIpoYear() > currentYear) {
                throw new IllegalArgumentException("IPO year cannot be in the future");
            }
        }

        try {
            // Update in database
            databaseManager.updateInstrument(
                    upperCurrentSymbol,
                    request.getSymbol(),
                    request.getName(),
                    request.getType().name(),
                    request.getMarketCap(),
                    request.getCountry(),
                    request.getIpoYear(),
                    request.getSector(),
                    request.getIndustry()
            );

            // Create and return the updated instrument object
            Instrument instrument = new Instrument(
                    request.getSymbol(),
                    request.getName(),
                    request.getType(),
                    request.getMarketCap(),
                    request.getCountry(),
                    request.getIpoYear(),
                    request.getSector(),
                    request.getIndustry()
            );

            logger.info("Successfully updated instrument: {} -> {}", currentSymbol, request.getSymbol());
            return instrument;

        } catch (SQLException e) {
            logger.error("Database error updating instrument: {}", currentSymbol, e);
            throw e;
        } catch (Exception e) {
            logger.error("Unexpected error updating instrument: {}", currentSymbol, e);
            throw new SQLException("Failed to update instrument: " + e.getMessage(), e);
        }
    }

    /**
     * Delete a financial instrument and all its associated data.
     *
     * @param symbol The symbol of the instrument to delete
     * @return Summary of deletion operation
     * @throws IllegalArgumentException if instrument not found
     * @throws SQLException if database operation fails
     */
    public String deleteInstrument(String symbol) throws SQLException {
        logger.info("Deleting instrument: {}", symbol);

        if (symbol == null || symbol.trim().isEmpty()) {
            throw new IllegalArgumentException("Symbol cannot be null or empty");
        }

        String upperSymbol = symbol.trim().toUpperCase();

        // Check if the instrument exists
        if (!databaseManager.symbolExists(upperSymbol)) {
            throw new IllegalArgumentException("Instrument not found: " + symbol);
        }

        try {
            // Delete instrument and all associated data
            String result = databaseManager.deleteInstrumentAndData(upperSymbol);

            logger.info("Successfully deleted instrument: {}", symbol);
            return result;

        } catch (SQLException e) {
            logger.error("Database error deleting instrument: {}", symbol, e);
            throw e;
        } catch (Exception e) {
            logger.error("Unexpected error deleting instrument: {}", symbol, e);
            throw new SQLException("Failed to delete instrument: " + e.getMessage(), e);
        }
    }
}
