package com.investment.service;

import com.investment.api.model.CreateWatchListRequest;
import com.investment.api.model.RecalculatePerformanceResponse;
import com.investment.api.model.UpdateWatchListRequest;
import com.investment.api.model.WatchListUpdateRequest;
import com.investment.api.model.WatchListUpdateResponse;
import com.investment.api.model.BollingerBandRequest;
import com.investment.api.model.DMIRequest;
import com.investment.database.DatabaseManager;
import com.investment.model.OHLCV;
import com.investment.model.WatchListItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service for managing watch list operations.
 * Follows garbage-free patterns for low-latency trading systems.
 */
@Service
public class WatchListService {

    private static final Logger logger = LoggerFactory.getLogger(WatchListService.class);

    private final DatabaseManager databaseManager;
    private final OHLCVService ohlcvService;
    private final BollingerBandService bollingerBandService;
    private final DMIService dmiService;

    public WatchListService(DatabaseManager databaseManager, OHLCVService ohlcvService,
                           BollingerBandService bollingerBandService, DMIService dmiService) {
        this.databaseManager = databaseManager;
        this.ohlcvService = ohlcvService;
        this.bollingerBandService = bollingerBandService;
        this.dmiService = dmiService;
    }

    /**
     * Create a new watch list item.
     */
    public WatchListItem createWatchListItem(CreateWatchListRequest request) throws SQLException {
        logger.info("Creating new watch list item: {}", request);

        // Validate that the symbol exists in instruments table
        if (!databaseManager.symbolExists(request.getSymbol())) {
            throw new IllegalArgumentException("Symbol not found in instruments: " + request.getSymbol());
        }

        // Check if symbol already exists in watch list
        if (databaseManager.symbolExistsInWatchList(request.getSymbol())) {
            throw new IllegalArgumentException("Symbol already exists in watch list: " + request.getSymbol());
        }

        WatchListItem item = request.toWatchListItem();

        Long id = databaseManager.createWatchListItem(
                item.getDisplayIndex(),
                item.getSymbol(),
                java.sql.Date.valueOf(item.getStartDate()),
                item.getRemarks()
        );

        item.setId(id);

        logger.info("Created watch list item with ID: {}", item.getId());
        return item;
    }

    /**
     * Get a watch list item by ID.
     */
    public Optional<WatchListItem> getWatchListItemById(Long id) throws SQLException {
        logger.debug("Retrieving watch list item by ID: {}", id);

        Map<String, Object> data = databaseManager.getWatchListItemById(id);
        if (data != null) {
            return Optional.of(mapDataToWatchListItem(data));
        }

        return Optional.empty();
    }

    /**
     * Get all watch list items ordered by display index.
     */
    public List<WatchListItem> getAllWatchListItems() throws SQLException {
        logger.debug("Retrieving all watch list items");

        List<Map<String, Object>> data = databaseManager.getWatchListItems();
        List<WatchListItem> items = new ArrayList<>();

        for (Map<String, Object> row : data) {
            items.add(mapDataToWatchListItem(row));
        }

        logger.debug("Retrieved {} watch list items", items.size());
        return items;
    }

    /**
     * Update a watch list item.
     */
    public WatchListItem updateWatchListItem(Long id, UpdateWatchListRequest request) throws SQLException {
        logger.info("Updating watch list item ID: {} with request: {}", id, request);

        if (!request.hasUpdates()) {
            throw new IllegalArgumentException("No update fields provided");
        }

        Optional<WatchListItem> existingItem = getWatchListItemById(id);
        if (existingItem.isEmpty()) {
            throw new IllegalArgumentException("Watch list item not found: " + id);
        }

        WatchListItem item = existingItem.get();

        // Update fields if provided
        if (request.getDisplayIndex() != null) {
            item.setDisplayIndex(request.getDisplayIndex());
        }

        if (request.getRemarks() != null) {
            item.setRemarks(request.getRemarks());
        }

        if (request.hasPerformanceUpdates()) {
            item.updatePerformance(
                    request.getOneMonthPerf(),
                    request.getThreeMonthPerf(),
                    request.getSixMonthPerf()
            );
        }

        // Save to database
        databaseManager.updateWatchListItem(
                item.getId(),
                item.getDisplayIndex(),
                item.getRemarks(),
                item.getOneMonthPerf(),
                item.getThreeMonthPerf(),
                item.getSixMonthPerf()
        );

        logger.info("Updated watch list item ID: {}", id);
        return item;
    }

    /**
     * Update performance metrics for a watch list item.
     */
    public WatchListItem updateWatchListPerformance(Long id, BigDecimal oneMonthPerf, 
                                                   BigDecimal threeMonthPerf, BigDecimal sixMonthPerf) throws SQLException {
        logger.info("Updating performance for watch list item ID: {}", id);

        Optional<WatchListItem> existingItem = getWatchListItemById(id);
        if (existingItem.isEmpty()) {
            throw new IllegalArgumentException("Watch list item not found: " + id);
        }

        WatchListItem item = existingItem.get();
        item.updatePerformance(oneMonthPerf, threeMonthPerf, sixMonthPerf);

        // Save to database
        databaseManager.updateWatchListPerformance(id, oneMonthPerf, threeMonthPerf, sixMonthPerf);

        logger.info("Updated performance for watch list item ID: {}", id);
        return item;
    }

    /**
     * Delete a watch list item.
     */
    public boolean deleteWatchListItem(Long id) throws SQLException {
        logger.info("Deleting watch list item ID: {}", id);

        boolean deleted = databaseManager.deleteWatchListItem(id);

        if (deleted) {
            logger.info("Deleted watch list item ID: {}", id);
        } else {
            logger.warn("Watch list item not found for deletion: {}", id);
        }

        return deleted;
    }

    /**
     * Bulk reorder watch list items.
     */
    public void reorderWatchListItems(Map<Long, Integer> idToIndexMap) throws SQLException {
        logger.info("Reordering {} watch list items", idToIndexMap.size());

        // Validate all IDs exist
        for (Long id : idToIndexMap.keySet()) {
            if (getWatchListItemById(id).isEmpty()) {
                throw new IllegalArgumentException("Watch list item not found: " + id);
            }
        }

        // Perform bulk update
        databaseManager.updateWatchListDisplayIndexes(idToIndexMap);

        logger.info("Successfully reordered {} watch list items", idToIndexMap.size());
    }

    /**
     * Calculate and update performance metrics for all watch list items.
     * Returns detailed results of the operation.
     */
    public RecalculatePerformanceResponse calculateAndUpdateAllPerformance() throws SQLException {
        logger.info("Calculating performance for all watch list items");

        long startTime = System.currentTimeMillis();
        List<WatchListItem> items = getAllWatchListItems();

        List<String> successfulSymbols = new ArrayList<>();
        List<String> failedSymbols = new ArrayList<>();
        List<String> skippedSymbols = new ArrayList<>();

        int successfulUpdates = 0;
        int failedUpdates = 0;
        int skippedItems = 0;

        for (WatchListItem item : items) {
            try {
                // Calculate performance based on OHLCV data
                PerformanceMetrics metrics = calculatePerformanceMetrics(item.getSymbol(), item.getStartDate());

                if (metrics.hasData()) {
                    databaseManager.updateWatchListPerformance(
                            item.getId(),
                            metrics.getOneMonthPerf(),
                            metrics.getThreeMonthPerf(),
                            metrics.getSixMonthPerf()
                    );
                    successfulUpdates++;
                    successfulSymbols.add(item.getSymbol());
                    logger.debug("Successfully updated performance for symbol: {}", item.getSymbol());
                } else {
                    skippedItems++;
                    skippedSymbols.add(item.getSymbol());
                    logger.debug("Skipped symbol due to insufficient data: {}", item.getSymbol());
                }
            } catch (Exception e) {
                failedUpdates++;
                failedSymbols.add(item.getSymbol());
                logger.warn("Failed to calculate performance for symbol: {}", item.getSymbol(), e);
            }
        }

        long processingTime = System.currentTimeMillis() - startTime;

        RecalculatePerformanceResponse response = new RecalculatePerformanceResponse(
                items.size(),
                successfulUpdates,
                failedUpdates,
                skippedItems,
                successfulSymbols,
                failedSymbols,
                skippedSymbols,
                processingTime
        );

        logger.info("Performance recalculation completed: {}", response.getSummaryMessage());
        return response;
    }

    /**
     * Calculate performance metrics for a symbol from start date.
     * Performance is calculated as percentage change from N months ago to current price.
     */
    private PerformanceMetrics calculatePerformanceMetrics(String symbol, LocalDate startDate) {
        logger.debug("Calculating performance metrics for {} from {}", symbol, startDate);

        try {
            // Get current date and calculate reference dates
            LocalDate currentDate = LocalDate.now();
            LocalDate oneMonthAgo = currentDate.minusMonths(1);
            LocalDate threeMonthsAgo = currentDate.minusMonths(3);
            LocalDate sixMonthsAgo = currentDate.minusMonths(6);

            // Get current price (most recent OHLCV data)
            List<OHLCV> currentData = databaseManager.getOHLCVData(symbol, currentDate.minusDays(7), currentDate);
            if (currentData.isEmpty()) {
                logger.warn("No recent OHLCV data found for symbol: {}", symbol);
                return new PerformanceMetrics();
            }

            // Get the most recent closing price
            double currentPrice = currentData.get(currentData.size() - 1).getClose();
            logger.debug("Current price for {}: {}", symbol, currentPrice);

            PerformanceMetrics metrics = new PerformanceMetrics();

            // Calculate 1-month performance
            BigDecimal oneMonthPerf = calculatePerformanceForPeriod(symbol, oneMonthAgo, currentPrice);
            if (oneMonthPerf != null) {
                metrics.setOneMonthPerf(oneMonthPerf);
                logger.debug("1-month performance for {}: {}%", symbol, oneMonthPerf.multiply(BigDecimal.valueOf(100)));
            }

            // Calculate 3-month performance
            BigDecimal threeMonthPerf = calculatePerformanceForPeriod(symbol, threeMonthsAgo, currentPrice);
            if (threeMonthPerf != null) {
                metrics.setThreeMonthPerf(threeMonthPerf);
                logger.debug("3-month performance for {}: {}%", symbol, threeMonthPerf.multiply(BigDecimal.valueOf(100)));
            }

            // Calculate 6-month performance
            BigDecimal sixMonthPerf = calculatePerformanceForPeriod(symbol, sixMonthsAgo, currentPrice);
            if (sixMonthPerf != null) {
                metrics.setSixMonthPerf(sixMonthPerf);
                logger.debug("6-month performance for {}: {}%", symbol, sixMonthPerf.multiply(BigDecimal.valueOf(100)));
            }

            return metrics;

        } catch (Exception e) {
            logger.error("Error calculating performance metrics for symbol: {}", symbol, e);
            return new PerformanceMetrics();
        }
    }

    /**
     * Calculate performance percentage for a specific period.
     * Returns null if insufficient data is available.
     */
    private BigDecimal calculatePerformanceForPeriod(String symbol, LocalDate referenceDate, double currentPrice) {
        try {
            // Get historical data around the reference date (±3 days to handle weekends/holidays)
            LocalDate startDate = referenceDate.minusDays(3);
            LocalDate endDate = referenceDate.plusDays(3);

            List<OHLCV> historicalData = databaseManager.getOHLCVData(symbol, startDate, endDate);
            if (historicalData.isEmpty()) {
                logger.debug("No historical data found for {} around {}", symbol, referenceDate);
                return null;
            }

            // Find the closest date to our reference date
            OHLCV closestData = historicalData.stream()
                    .min((a, b) -> {
                        long diffA = Math.abs(a.getDate().toEpochDay() - referenceDate.toEpochDay());
                        long diffB = Math.abs(b.getDate().toEpochDay() - referenceDate.toEpochDay());
                        return Long.compare(diffA, diffB);
                    })
                    .orElse(null);

            if (closestData == null) {
                return null;
            }

            double historicalPrice = closestData.getClose();

            // Calculate percentage change: (current - historical) / historical
            double performanceDecimal = (currentPrice - historicalPrice) / historicalPrice;

            // Convert to BigDecimal with appropriate precision
            return BigDecimal.valueOf(performanceDecimal).setScale(6, RoundingMode.HALF_UP);

        } catch (Exception e) {
            logger.warn("Error calculating performance for {} at {}: {}", symbol, referenceDate, e.getMessage());
            return null;
        }
    }

    /**
     * Map database data to WatchListItem object.
     */
    private WatchListItem mapDataToWatchListItem(Map<String, Object> data) {
        WatchListItem item = new WatchListItem();

        item.setId((Long) data.get("id"));
        item.setDisplayIndex((Integer) data.get("display_index"));
        item.setSymbol((String) data.get("symbol"));
        
        java.sql.Date startDate = (java.sql.Date) data.get("start_date");
        if (startDate != null) {
            item.setStartDate(startDate.toLocalDate());
        }
        
        item.setRemarks((String) data.get("remarks"));
        item.setOneMonthPerf((BigDecimal) data.get("one_mo_perf"));
        item.setThreeMonthPerf((BigDecimal) data.get("three_mo_perf"));
        item.setSixMonthPerf((BigDecimal) data.get("six_mo_perf"));

        Timestamp createdTimestamp = (Timestamp) data.get("created_date");
        if (createdTimestamp != null) {
            item.setCreatedDate(createdTimestamp.toLocalDateTime());
        }

        Timestamp updatedTimestamp = (Timestamp) data.get("updated_date");
        if (updatedTimestamp != null) {
            item.setUpdatedDate(updatedTimestamp.toLocalDateTime());
        }

        return item;
    }

    /**
     * Update OHLCV data for all watch list symbols and optionally recalculate technical indicators.
     * This method performs a comprehensive update process including:
     * 1. OHLCV data updates for all watch list symbols
     * 2. Bollinger Bands recalculation (if requested)
     * 3. DMI indicators recalculation (if requested)
     */
    public WatchListUpdateResponse updateOHLCVDataForWatchList(WatchListUpdateRequest request) throws SQLException {
        logger.info("Starting OHLCV data update for watch list symbols: {}", request);

        long startTime = System.currentTimeMillis();
        List<WatchListItem> watchListItems = getAllWatchListItems();

        WatchListUpdateResponse response = new WatchListUpdateResponse(watchListItems.size(), 0, 0);
        List<WatchListUpdateResponse.SymbolUpdateResult> ohlcvResults = new ArrayList<>();

        // Phase 1: Update OHLCV data for each symbol
        logger.info("Phase 1: Updating OHLCV data for {} symbols", watchListItems.size());
        int ohlcvSuccessCount = 0;
        int ohlcvErrorCount = 0;
        int totalRecordsUpdated = 0;

        for (WatchListItem item : watchListItems) {
            String symbol = item.getSymbol();
            try {
                if (!request.isDryRun()) {
                    int recordsUpdated = ohlcvService.updateOHLCVData(symbol, null, null);
                    totalRecordsUpdated += recordsUpdated;

                    if (recordsUpdated > 0) {
                        ohlcvResults.add(new WatchListUpdateResponse.SymbolUpdateResult(
                            symbol, "success", String.format("Updated %d records", recordsUpdated)));
                        ohlcvSuccessCount++;
                        logger.debug("Successfully updated {} records for symbol: {}", recordsUpdated, symbol);
                    } else {
                        ohlcvResults.add(new WatchListUpdateResponse.SymbolUpdateResult(
                            symbol, "success", "No new data available"));
                        ohlcvSuccessCount++;
                        logger.debug("No new data available for symbol: {}", symbol);
                    }
                } else {
                    ohlcvResults.add(new WatchListUpdateResponse.SymbolUpdateResult(
                        symbol, "success", "Dry run - would update OHLCV data"));
                    ohlcvSuccessCount++;
                }

                // Add rate limiting delay
                Thread.sleep(100);

            } catch (Exception e) {
                ohlcvResults.add(new WatchListUpdateResponse.SymbolUpdateResult(
                    symbol, "error", null, e.getMessage()));
                ohlcvErrorCount++;
                logger.warn("Failed to update OHLCV data for symbol: {}", symbol, e);
            }
        }

        // Update response with OHLCV results
        response.setOhlcvSuccessCount(ohlcvSuccessCount);
        response.setOhlcvErrorCount(ohlcvErrorCount);
        response.setTotalRecordsUpdated(totalRecordsUpdated);
        response.getPhases().setOhlcv(ohlcvResults);

        // Phase 2: Recalculate technical indicators if requested
        if (request.isRecalculateTechnicalIndicators() && ohlcvSuccessCount > 0) {
            logger.info("Phase 2: Recalculating technical indicators for successfully updated symbols");

            // Get symbols that were successfully updated
            List<String> successfulSymbols = ohlcvResults.stream()
                .filter(result -> "success".equals(result.getStatus()))
                .map(WatchListUpdateResponse.SymbolUpdateResult::getSymbol)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);

            try {
                // Recalculate Bollinger Bands using INCREMENTAL mode
                BollingerBandRequest bbRequest = new BollingerBandRequest();
                bbRequest.setCalculationMode(BollingerBandRequest.CalculationMode.INCREMENTAL);
                bbRequest.setSymbols(successfulSymbols);
                bbRequest.setDryRun(request.isDryRun());

                var bbResponse = bollingerBandService.calculateBollingerBands(bbRequest);
                response.setBollingerBandsProcessed(bbResponse.getProcessedSymbols());

                Map<String, Object> bbResults = new HashMap<>();
                bbResults.put("processedSymbols", bbResponse.getProcessedSymbols());
                bbResults.put("totalRecordsUpdated", bbResponse.getTotalRecordsUpdated());
                bbResults.put("status", bbResponse.getStatus());
                response.getPhases().setBollingerBands(bbResults);

                logger.info("Bollinger Bands recalculation completed: {} symbols processed",
                           bbResponse.getProcessedSymbols());

            } catch (Exception e) {
                logger.warn("Failed to recalculate Bollinger Bands", e);
            }

            try {
                // Recalculate DMI using INCREMENTAL mode
                DMIRequest dmiRequest = new DMIRequest();
                dmiRequest.setCalculationMode(DMIRequest.CalculationMode.INCREMENTAL);
                dmiRequest.setSymbols(successfulSymbols);
                dmiRequest.setDryRun(request.isDryRun());

                var dmiResponse = dmiService.calculateDMI(dmiRequest);
                response.setDmiProcessed(dmiResponse.getProcessedSymbols());

                Map<String, Object> dmiResults = new HashMap<>();
                dmiResults.put("processedSymbols", dmiResponse.getProcessedSymbols());
                dmiResults.put("totalRecordsUpdated", dmiResponse.getTotalRecordsUpdated());
                dmiResults.put("status", dmiResponse.getStatus());
                response.getPhases().setDmi(dmiResults);

                logger.info("DMI recalculation completed: {} symbols processed",
                           dmiResponse.getProcessedSymbols());

            } catch (Exception e) {
                logger.warn("Failed to recalculate DMI indicators", e);
            }
        }

        // Finalize response
        long processingTime = System.currentTimeMillis() - startTime;
        response.setProcessingTimeMs(processingTime);

        String summary = String.format(
            "Watch list update completed: %d/%d symbols updated successfully, %d technical indicator calculations, %d total records updated (%.1fs)",
            ohlcvSuccessCount, watchListItems.size(),
            response.getBollingerBandsProcessed() + response.getDmiProcessed(),
            totalRecordsUpdated, processingTime / 1000.0);
        response.setSummary(summary);

        logger.info("Watch list OHLCV update completed: {}", summary);
        return response;
    }

    /**
     * Helper class for performance metrics calculation.
     */
    private static class PerformanceMetrics {
        private BigDecimal oneMonthPerf;
        private BigDecimal threeMonthPerf;
        private BigDecimal sixMonthPerf;

        public boolean hasData() {
            return oneMonthPerf != null || threeMonthPerf != null || sixMonthPerf != null;
        }

        public BigDecimal getOneMonthPerf() { return oneMonthPerf; }
        public BigDecimal getThreeMonthPerf() { return threeMonthPerf; }
        public BigDecimal getSixMonthPerf() { return sixMonthPerf; }

        public void setOneMonthPerf(BigDecimal oneMonthPerf) { this.oneMonthPerf = oneMonthPerf; }
        public void setThreeMonthPerf(BigDecimal threeMonthPerf) { this.threeMonthPerf = threeMonthPerf; }
        public void setSixMonthPerf(BigDecimal sixMonthPerf) { this.sixMonthPerf = sixMonthPerf; }
    }
}
